#!/usr/bin/env python3
"""
Configuration management for the RepoSense AI application
Handles loading, saving, and validation of configuration settings
"""

import json
import logging
import os
from dataclasses import asdict
from datetime import datetime
from enum import Enum
from pathlib import Path

from models import (
    Config,
    HistoricalScanConfig,
    HistoricalScanStatus,
    RepositoryConfig,
    User,
    UserRole,
)


class ConfigJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles enums and datetime objects"""

    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class ConfigManager:
    """Manager for application configuration"""

    def __init__(self, config_path: str | None = None):
        # Standard config path resolution - only use data directory
        if config_path is None:
            if os.path.exists("/app/data/config.json"):
                config_path = "/app/data/config.json"
            elif os.path.exists("data/config.json"):
                config_path = "data/config.json"
            else:
                config_path = "/app/data/config.json"  # Default to data directory

        self.config_path = config_path
        self.logger = logging.getLogger(__name__)

    def load_config(self) -> Config:
        """Load configuration from JSON file"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, "r") as f:
                    config_dict = json.load(f)

                    # Remove users from config_dict if present (users are now in database)
                    if "users" in config_dict:
                        del config_dict["users"]

                    # Remove repositories from config_dict if present (repositories are now in database)
                    if "repositories" in config_dict:
                        del config_dict["repositories"]

                    # Ensure server config exists (for backward compatibility)
                    if "server" not in config_dict or config_dict["server"] is None:
                        from models import ServerConfig

                        # Create server config as dict for dataclass constructor
                        config_dict["server"] = {
                            "name": "default",
                            "description": "",
                            "base_url": config_dict.get("svn_server_url", ""),
                            "default_username": config_dict.get("svn_server_username"),
                            "default_password": config_dict.get("svn_server_password"),
                            "enabled": True,
                        }
                        self.logger.info(
                            "Initialized default server configuration for backward compatibility"
                        )

                    # Convert server dict to ServerConfig object if needed
                    if isinstance(config_dict.get("server"), dict):
                        from models import ServerConfig

                        server_dict = config_dict["server"]
                        # Ensure all required fields exist with defaults
                        server_dict.setdefault("name", "default")
                        server_dict.setdefault("description", "")
                        server_dict.setdefault("base_url", "")
                        server_dict.setdefault("default_username", None)
                        server_dict.setdefault("default_password", None)
                        server_dict.setdefault("enabled", True)
                        server_dict.setdefault("id", str(__import__("uuid").uuid4()))

                        # Convert dict to ServerConfig object
                        config_dict["server"] = ServerConfig(**server_dict)
                        self.logger.info("Converted server dict to ServerConfig object")

                    config = Config(**config_dict)

                    # Apply environment variable overrides
                    config = self._apply_environment_overrides(config)

                    self.logger.info(f"Configuration loaded from {self.config_path}")
                    return config
            except Exception as e:
                self.logger.error(f"Error loading config: {e}")
                return self._create_default_config()
        else:
            # Create default config
            return self._create_default_config()

    def save_config(self, config: Config):
        """Save configuration to JSON file"""
        try:
            # Ensure the directory exists
            Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)

            with open(self.config_path, "w") as f:
                json.dump(asdict(config), f, indent=2, cls=ConfigJSONEncoder)

            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            raise

    def _apply_environment_overrides(self, config: Config) -> Config:
        """Apply environment variable overrides to configuration

        Environment variables are now ONLY used for deployment overrides.
        The web interface is the primary configuration method.
        Only apply environment overrides if explicitly set for deployment.
        """
        import os

        # Only apply environment overrides if they exist AND we're in a deployment context
        # This reduces dependency on environment variables for normal operation

        # Ollama configuration - only override if explicitly set
        if "OLLAMA_BASE_URL" in os.environ:
            config.ollama_host = os.environ["OLLAMA_BASE_URL"]
            self.logger.info(
                f"🔧 Ollama host overridden by environment: {config.ollama_host}"
            )

        if "OLLAMA_MODEL" in os.environ:
            config.ollama_model = os.environ["OLLAMA_MODEL"]
            self.logger.info(
                f"🔧 Ollama model overridden by environment: {config.ollama_model}"
            )

        # Web interface configuration - only for deployment
        if "REPOSENSE_AI_WEB_HOST" in os.environ:
            config.web_host = os.environ["REPOSENSE_AI_WEB_HOST"]
            self.logger.info(
                f"🔧 Web host overridden by environment: {config.web_host}"
            )

        if "REPOSENSE_AI_WEB_PORT" in os.environ:
            try:
                config.web_port = int(os.environ["REPOSENSE_AI_WEB_PORT"])
                self.logger.info(
                    f"🔧 Web port overridden by environment: {config.web_port}"
                )
            except ValueError:
                self.logger.warning(
                    f"⚠️ Invalid web port in environment: {os.environ['REPOSENSE_AI_WEB_PORT']}"
                )

        # Log if no environment overrides are active (normal operation)
        env_vars = [
            "OLLAMA_BASE_URL",
            "OLLAMA_MODEL",
            "REPOSENSE_AI_WEB_HOST",
            "REPOSENSE_AI_WEB_PORT",
        ]
        active_overrides = [var for var in env_vars if var in os.environ]

        if not active_overrides:
            self.logger.info(
                "✅ Using configuration file values (no environment overrides)"
            )
        else:
            self.logger.info(
                f"🔧 Active environment overrides: {', '.join(active_overrides)}"
            )

        return config

    def _create_default_config(self) -> Config:
        """Create and save a default configuration"""
        return self._create_fresh_config_with_current_schema()

    def _create_fresh_config_with_current_schema(self) -> Config:
        """Create a completely fresh config that matches current system expectations

        This ensures the config structure matches what the current codebase expects,
        regardless of what was in any previous config file.
        """
        from models import ServerConfig

        # Create config with ALL current schema expectations
        config = Config(
            # Server configuration (new in current schema)
            server=ServerConfig(
                name="default",
                description="",
                base_url="",
                default_username=None,
                default_password=None,
                enabled=True,
            ),
            # All other fields use their dataclass defaults
            # AI settings with current defaults
            ollama_host="http://localhost:11434",
            ollama_model="qwen3",
            ollama_model_documentation=None,
            ollama_model_code_review=None,
            ollama_model_risk_assessment=None,
            # All other fields will use Config dataclass defaults
        )

        self.logger.info("Created fresh config with current schema")

        # Apply environment variable overrides
        config = self._apply_environment_overrides(config)

        try:
            self.save_config(config)
            self.logger.info("Default configuration created")
        except Exception as e:
            self.logger.warning(f"Could not save default config: {e}")
        return config

    def validate_config(self, config: Config) -> tuple[bool, list]:
        """Validate configuration and return (is_valid, errors)"""
        errors = []

        # Note: Repository validation removed - users should be able to save
        # basic configuration without repositories configured yet.
        # Repositories will be validated when monitoring is started.

        # Validate intervals
        if config.check_interval < 30:
            errors.append("Check interval must be at least 30 seconds")

        # Validate ports
        if not (1 <= config.smtp_port <= 65535):
            errors.append("SMTP port must be between 1 and 65535")

        if not (1 <= config.web_port <= 65535):
            errors.append("Web port must be between 1 and 65535")

        # Validate email settings if email is enabled
        if config.send_emails:
            if not config.email_from:
                errors.append("Email 'from' address is required when email is enabled")
            if not config.email_recipients:
                errors.append("Email recipients are required when email is enabled")

        # Validate output directory
        if not config.output_dir:
            errors.append("Output directory is required")

        is_valid = len(errors) == 0
        return is_valid, errors

    def validate_config_for_monitoring(self, config: Config) -> tuple[bool, list]:
        """Validate configuration specifically for starting monitoring (requires repositories)"""
        errors = []

        # Import here to avoid circular imports
        from repository_database import RepositoryDatabase

        # Validate repositories are configured for monitoring
        repo_db = RepositoryDatabase("/app/data/reposense.db")
        all_repos = repo_db.get_all_repositories()
        if not all_repos:
            errors.append(
                "At least one repository must be configured to start monitoring"
            )

        enabled_repos = repo_db.get_enabled_repositories()
        if not enabled_repos:
            errors.append("At least one repository must be enabled to start monitoring")

        is_valid = len(errors) == 0
        return is_valid, errors

    def update_config_from_form(self, config: Config, form_data: dict) -> Config:
        """Update configuration from web form data

        This updates the config file values. Environment variables will still
        override these values when the configuration is loaded.
        """
        try:
            self.logger.info("📝 Updating configuration from web form")
            self.logger.info(f"   Current ollama_host: {config.ollama_host}")
            self.logger.info(
                f"   Form ollama_host: {form_data.get('ollama_host', 'NOT_PROVIDED')}"
            )

            # Update server configuration
            from models import ServerConfig

            # Handle server config whether it's an object or dict
            if config.server:
                if isinstance(config.server, dict):
                    # Convert dict to ServerConfig object
                    server_config = ServerConfig(**config.server)
                else:
                    # Already a ServerConfig object
                    server_config = config.server
            else:
                # Create new ServerConfig
                server_config = ServerConfig()

            # Update server config fields
            server_config.name = form_data.get(
                "server_name", server_config.name or "default"
            )
            server_config.description = form_data.get(
                "server_description", server_config.description or ""
            )
            server_config.base_url = form_data.get(
                "svn_server_url", server_config.base_url or ""
            )
            server_config.default_username = (
                form_data.get("svn_server_username") or server_config.default_username
            )
            server_config.default_password = (
                form_data.get("svn_server_password") or server_config.default_password
            )

            # Create new config with form values, using current config as defaults
            updated_config = Config(
                server=server_config,  # Use updated server config
                ollama_host=form_data.get("ollama_host", config.ollama_host),
                ollama_model=form_data.get("ollama_model", config.ollama_model),
                ollama_model_documentation=form_data.get("ollama_model_documentation")
                or config.ollama_model_documentation,
                ollama_model_code_review=form_data.get("ollama_model_code_review")
                or config.ollama_model_code_review,
                ollama_model_risk_assessment=form_data.get(
                    "ollama_model_risk_assessment"
                )
                or config.ollama_model_risk_assessment,
                use_enhanced_prompts="use_enhanced_prompts" in form_data,
                enhanced_prompts_fallback="enhanced_prompts_fallback" in form_data,
                check_interval=int(
                    form_data.get("check_interval", config.check_interval)
                ),
                svn_server_url=form_data.get("svn_server_url") or config.svn_server_url,
                svn_server_username=form_data.get("svn_server_username")
                or config.svn_server_username,
                svn_server_password=form_data.get("svn_server_password")
                or config.svn_server_password,
                smtp_host=form_data.get("smtp_host", config.smtp_host),
                smtp_port=int(form_data.get("smtp_port", config.smtp_port)),
                smtp_username=form_data.get("smtp_username") or config.smtp_username,
                smtp_password=form_data.get("smtp_password") or config.smtp_password,
                email_from=form_data.get("email_from", config.email_from),
                email_recipients=[
                    r.strip()
                    for r in form_data.get(
                        "email_recipients", ",".join(config.email_recipients)
                    ).split(",")
                    if r.strip()
                ],
                generate_docs="generate_docs" in form_data,
                send_emails="send_emails" in form_data,
                web_enabled=True,
                web_port=config.web_port,
                web_host=config.web_host,
                web_secret_key=config.web_secret_key,
                web_log_entries=int(
                    form_data.get("web_log_entries", config.web_log_entries)
                ),
                output_dir=config.output_dir,
                # LDAP Configuration
                ldap_sync_enabled="ldap_sync_enabled" in form_data,
                ldap_server=form_data.get("ldap_server") or config.ldap_server,
                ldap_port=int(form_data.get("ldap_port", config.ldap_port)),
                ldap_use_ssl="ldap_use_ssl" in form_data,
                ldap_bind_dn=form_data.get("ldap_bind_dn") or config.ldap_bind_dn,
                ldap_bind_password=form_data.get("ldap_bind_password")
                or config.ldap_bind_password,
                ldap_user_base_dn=form_data.get("ldap_user_base_dn")
                or config.ldap_user_base_dn,
                ldap_user_filter=form_data.get("ldap_user_filter")
                or config.ldap_user_filter,
                ldap_sync_interval=int(
                    form_data.get("ldap_sync_interval", config.ldap_sync_interval)
                ),
                ldap_username_attr=form_data.get("ldap_username_attr")
                or config.ldap_username_attr,
                ldap_email_attr=form_data.get("ldap_email_attr")
                or config.ldap_email_attr,
                ldap_fullname_attr=form_data.get("ldap_fullname_attr")
                or config.ldap_fullname_attr,
                ldap_phone_attr=form_data.get("ldap_phone_attr")
                or config.ldap_phone_attr,
                ldap_groups_attr=form_data.get("ldap_groups_attr")
                or config.ldap_groups_attr,
                ldap_group_role_mapping=config.ldap_group_role_mapping,  # Keep existing mappings
                ldap_default_role=form_data.get("ldap_default_role")
                or config.ldap_default_role,
            )

            self.logger.info(
                f"📄 Config file will be saved with ollama_host: {updated_config.ollama_host}"
            )

            # NOTE: Environment variables will override these values when config is loaded
            # This is intentional - environment variables have higher priority

            return updated_config

        except (ValueError, TypeError) as e:
            self.logger.error(f"Error updating config from form: {e}")
            raise ValueError(f"Invalid form data: {e}")

    def add_repository_from_form(
        self, config: Config, form_data: dict
    ) -> RepositoryConfig:
        """Add a new repository from web form data"""
        name = form_data.get("name", "").strip()
        url = form_data.get("url", "").strip()

        # Validate required fields
        if not name:
            raise ValueError("Repository name is required")
        if not url:
            raise ValueError("Repository URL is required")

        # Import here to avoid circular imports
        from repository_database import RepositoryDatabase

        repo_db = RepositoryDatabase("/app/data/reposense.db")

        # Check for duplicate name
        existing_repo_by_name = repo_db.get_repository_by_name(name)
        if existing_repo_by_name:
            raise ValueError(f"A repository with the name '{name}' already exists")

        # Check for duplicate URL
        if repo_db.repository_exists(url=url):
            raise ValueError(f"A repository with the URL '{url}' already exists")

        # Parse email recipients
        email_recipients = []
        if form_data.get("email_recipients"):
            email_recipients = [
                email.strip()
                for email in form_data.get("email_recipients", "").split(",")
                if email.strip()
            ]

        repo = RepositoryConfig(
            name=name,
            url=url,
            username=form_data.get("username") or None,
            password=form_data.get("password") or None,
            enabled=form_data.get("enabled", "off") == "on",
            email_recipients=email_recipients,
            risk_aggressiveness=form_data.get("risk_aggressiveness", "BALANCED"),
            risk_description=form_data.get("risk_description", "").strip(),
            branch_path=form_data.get("branch_path", "trunk").strip() or "trunk",
            monitor_all_branches=form_data.get("monitor_all_branches", "off") == "on",
        )

        # Save repository to database instead of config
        if repo_db.create_repository(repo):
            return repo
        else:
            raise ValueError("Failed to save repository to database")

    def update_repository_from_form(
        self, config: Config, repo_id: str, form_data: dict
    ) -> bool:
        """Update an existing repository from web form data"""
        # Import here to avoid circular imports
        from repository_database import RepositoryDatabase

        repo_db = RepositoryDatabase("/app/data/reposense.db")
        repo = repo_db.get_repository_by_id(repo_id)

        if repo:
            # Parse email recipients
            email_recipients = []
            if form_data.get("email_recipients"):
                email_recipients = [
                    email.strip()
                    for email in form_data.get("email_recipients", "").split(",")
                    if email.strip()
                ]

            # Parse product documentation files
            product_doc_files = []
            if form_data.get("product_documentation_files"):
                product_doc_files = [
                    file.strip()
                    for file in form_data.get("product_documentation_files", "").split(
                        "\n"
                    )
                    if file.strip()
                ]

            repo.name = form_data.get("name", repo.name)
            repo.url = form_data.get("url", repo.url)
            repo.username = form_data.get("username") or None
            repo.password = form_data.get("password") or None
            repo.enabled = form_data.get("enabled", "off") == "on"
            repo.email_recipients = email_recipients
            repo.product_documentation_files = product_doc_files
            repo.risk_aggressiveness = form_data.get(
                "risk_aggressiveness", repo.risk_aggressiveness or "BALANCED"
            )
            repo.risk_description = form_data.get("risk_description", "").strip()
            repo.branch_path = (
                form_data.get("branch_path", repo.branch_path or "trunk").strip()
                or "trunk"
            )
            repo.monitor_all_branches = (
                form_data.get("monitor_all_branches", "off") == "on"
            )

            # Update repository in database
            return repo_db.update_repository(repo)
        return False

    def get_config_path(self) -> str:
        """Get the configuration file path"""
        return self.config_path
