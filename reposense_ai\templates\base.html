<!DOCTYPE html>
<html lang="en" data-bs-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RepoSense AI{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bs-primary: #2563eb;
            --bs-primary-rgb: 37, 99, 235;
            --bs-secondary: #64748b;
            --bs-success: #059669;
            --bs-danger: #dc2626;
            --bs-warning: #d97706;
            --bs-info: #0891b2;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: #f8fafc;
            font-size: 14px;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            border-right: 1px solid #e2e8f0;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid #475569;
        }

        .sidebar-brand h4 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.25rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .nav-link {
            color: #cbd5e1 !important;
            padding: 0.75rem 1.25rem;
            border-radius: 0;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white !important;
        }

        .nav-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            color: white !important;
            border-left-color: var(--bs-primary);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 12px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-size: 0.95rem;
        }

        .card {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
            border-radius: 12px 12px 0 0 !important;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            transform: translateY(-1px);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-running {
            background-color: #059669;
            box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
        }

        .status-stopped {
            background-color: #dc2626;
            animation: none;
        }

        .status-warning {
            background-color: #d97706;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
            }
        }

        .form-control,
        .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .log-container {
            background-color: #1e293b;
            color: #e2e8f0;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 0.875rem;
            height: 400px;
            overflow-y: auto;
            line-height: 1.5;
            resize: vertical;
            min-height: 200px;
            max-height: 80vh;
        }

        .badge {
            border-radius: 6px;
            font-weight: 500;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .modal-header {
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0;
        }

        .modal-footer {
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 12px 12px;
        }

        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--bs-primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-outline-secondary {
            border-color: #d1d5db;
            color: #6b7280;
        }

        .btn-outline-secondary:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
            color: #374151;
        }

        .btn-outline-primary {
            border-color: var(--bs-primary);
            color: var(--bs-primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: white;
        }

        /* Global dropdown z-index fix - more aggressive approach */
        .dropdown-menu {
            z-index: 9999 !important;
            position: absolute !important;
        }

        .dropdown {
            position: relative;
            z-index: 40;
        }

        /* When dropdown is open, ensure it's above everything */
        .dropdown.show {
            z-index: 9998 !important;
            position: relative !important;
        }

        .dropdown.show .dropdown-menu {
            z-index: 9999 !important;
            position: absolute !important;
        }

        .btn-group {
            position: relative;
            z-index: 30;
        }

        /* Specific fix for dropdowns inside button groups */
        .btn-group .dropdown {
            z-index: 40;
            position: relative !important;
        }

        .btn-group .dropdown.show {
            z-index: 9998 !important;
            position: relative !important;
        }

        /* Force dropdown menu to appear above button groups */
        .btn-group .dropdown-menu {
            z-index: 9999 !important;
            position: absolute !important;
        }

        /* Override any Bootstrap/Popper positioning */
        .dropdown-menu[data-popper-placement] {
            z-index: 9999 !important;
        }

        /* Nuclear option - completely break out of stacking context */
        .btn-group .dropdown-menu {
            position: fixed !important;
            z-index: 99999 !important;
            transform: translateZ(0) !important;
        }

        /* Reset any transform on parent elements that might create stacking context */
        .btn-group {
            transform: none !important;
            will-change: auto !important;
        }

        .table {
            transform: none !important;
            will-change: auto !important;
        }

        /* Ensure dropdown toggle button maintains proper stacking */
        .btn-group .dropdown-toggle {
            position: relative;
            z-index: 1;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
                padding-top: 4rem;
            }

            .page-header {
                padding: 1rem;
            }

            .page-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-brain me-2"></i>RepoSense AI</h4>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}"
                        href="{{ url_for('index') }}">
                        <i class="fas fa-home"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'repositories_page' %}active{% endif %}"
                        href="{{ url_for('repositories_page') }}">
                        <i class="fas fa-code-branch"></i>Repositories
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'users_page' %}active{% endif %}"
                        href="{{ url_for('users_page') }}">
                        <i class="fas fa-users"></i>Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'ldap_sync_page' %}active{% endif %}"
                        href="{{ url_for('ldap_sync_page') }}">
                        <i class="fas fa-users-cog"></i>LDAP Sync
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'documents_page' or request.endpoint == 'view_document' %}active{% endif %}"
                        href="{{ url_for('documents_page') }}">
                        <i class="fas fa-file-text"></i>Documents
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'config_page' %}active{% endif %}"
                        href="{{ url_for('config_page') }}">
                        <i class="fas fa-cog"></i>Configuration
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'logs_page' %}active{% endif %}"
                        href="{{ url_for('logs_page') }}">
                        <i class="fas fa-file-alt"></i>Logs
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show"
            role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function (event) {
            const sidebar = document.querySelector('.sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !menuBtn.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
    </script>

    <!-- Toast Container for Processing Notifications -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
        <div id="processing-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i id="toast-icon" class="fas fa-info-circle text-info me-2"></i>
                <strong class="me-auto" id="toast-title">Processing Status</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toast-body">
                Processing documents...
            </div>
        </div>
    </div>

    <!-- Global Processing Notification Functions -->
    <script>
        // Global toast notification functions
        window.showProcessingToast = function (title, message, type = 'info') {
            const toast = document.getElementById('processing-toast');
            const toastTitle = document.getElementById('toast-title');
            const toastBody = document.getElementById('toast-body');
            const toastIcon = document.getElementById('toast-icon');

            // Set content
            toastTitle.textContent = title;
            toastBody.textContent = message;

            // Set icon and color based on type
            toastIcon.className = 'me-2 ';
            switch (type) {
                case 'success':
                    toastIcon.className += 'fas fa-check-circle text-success';
                    break;
                case 'warning':
                    toastIcon.className += 'fas fa-exclamation-triangle text-warning';
                    break;
                case 'error':
                    toastIcon.className += 'fas fa-times-circle text-danger';
                    break;
                case 'processing':
                    toastIcon.className += 'fas fa-spinner fa-spin text-info';
                    break;
                default:
                    toastIcon.className += 'fas fa-info-circle text-info';
            }

            // Show toast
            const bsToast = new bootstrap.Toast(toast, {
                autohide: type !== 'processing', // Don't auto-hide processing toasts
                delay: type === 'success' ? 3000 : 5000
            });
            bsToast.show();

            return bsToast;
        };

        // Hide processing toast
        window.hideProcessingToast = function () {
            const toast = document.getElementById('processing-toast');
            const bsToast = bootstrap.Toast.getInstance(toast);
            if (bsToast) {
                bsToast.hide();
            }
        };
    </script>

    {% block scripts %}{% endblock %}
</body>

</html>